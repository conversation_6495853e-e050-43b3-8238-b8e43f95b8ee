# -*- coding: utf-8 -*-

import json
from llama_index.llms.openai import OpenAI
from llama_index.core.llms import ChatMessage
from llama_index.core.prompts import PromptTemplate
import sys
sys.path.append('/Users/<USER>/ai-service') 
from common.config.config import (LLM_MODEL, LLM_API_KEY, LLM_API_URL, LLM_FORMAT_MODLE)

# 预定义示例响应（用于few-shot提示）
example1_response = json.dumps(
    {
        "info": {"name": "张三", "age": "25岁", "email": "<EMAIL>"},
        "hobby": ["唱歌"]
    },
    ensure_ascii=False
)
example2_response = json.dumps(
    {
        "info": {"name": "李四", "age": "30岁", "email": "<EMAIL>"},
        "hobby": ["跳舞", "游泳"]
    },
    ensure_ascii=False
)
example3_response = json.dumps(
    {
        "info": {"name": "王五", "age": "40岁", "email": "<EMAIL>"},
        "hobby": ["Rap", "篮球"]
    },
    ensure_ascii=False
)

template = f"""提取name、age、email和hobby（数组类型），输出包含info层和hobby数组的JSON。
            示例：
            Q：我叫张三，今年25岁，邮箱是********************，爱好是唱歌
            A：{example1_response}
            
            Q：我叫李四，今年30岁，邮箱是****************，平时喜欢跳舞和游泳
            A：{example2_response}
            
            Q：我的邮箱是******************，今年40岁，名字是王五，会Rap和打篮球
            A：{example3_response}"""

prompt_template = PromptTemplate(template)
review_prompt = prompt_template.format(example1_response=example1_response, example2_response=example2_response, example3_response=example3_response)

def test():
    llm = OpenAI(
        api_key=LLM_API_KEY,
        api_base=LLM_API_URL,
        default_headers={"model": LLM_FORMAT_MODLE},
        additional_kwargs={
        "response_format": {"type": "json_object"}
    }
    )

    messages = [ChatMessage(role="user", content=review_prompt)]
    response = llm.chat(messages=messages)
    print(response)
    try:
        data = json.loads(response.message.content)
        print(data)
    except json.JSONDecodeError:
        print("JSON解析错误")

if __name__ == "__main__":
    test()