# -*- coding: utf-8 -*-
# @Time   : 2025/6/5 11:48
# <AUTHOR> 王志强
# @Email  : <EMAIL>
# @File   : result_util.py
# @Project: ai-service
# @Desc   : 主要实现了xxx功能

from common.utils.logger_util import get_logger, log_function_call

# 获取日志器
logger = get_logger("result_util")

def get_ret_result(data={}, status=0, message=''):
    """
    构建统一的返回结果格式

    Args:
        data: 返回的数据
        status: 状态码，0表示成功
        message: 消息内容

    Returns:
        dict: 统一格式的返回结果
    """
    log_function_call("get_ret_result", args=(f"data_keys={list(data.keys()) if isinstance(data, dict) else type(data).__name__}", status, message))

    if message:
        ret = {
            'status': status,
            'message': message,
            'data': data
        }
        if status != 0:
            logger.warning(f"返回错误结果 - 状态码: {status}, 消息: {message}")
        else:
            logger.debug(f"返回带消息的结果 - 状态码: {status}, 消息: {message}")
    else:
        ret = {
            'status': 0,
            'message': 'success',
            'data': data
        }
        logger.debug("返回成功结果")

    logger.debug(f"构建返回结果完成，状态码: {ret['status']}")
    return ret
