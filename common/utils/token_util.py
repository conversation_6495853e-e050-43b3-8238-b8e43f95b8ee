# -*- coding: utf-8 -*-
# @Time   : 2025/6/10 11:48
# <AUTHOR> 和森
# @Email  : <EMAIL>
# @File   : token_util.py
# @Project: ai-service
# @Desc   : 主要实现了xxx功能

import tiktoken
from datetime import datetime
from common.utils.logger_util import get_logger, log_function_call, log_execution_time

# 获取日志器
logger = get_logger("token_util")


def count_tokens(text: str) -> int:
    """
    计算文本的 token 数量。

    Args:
        text (str): 输入文本。

    Returns:
        int: token 数量。
    """
    start_time = datetime.now()
    log_function_call("count_tokens", args=(f"text_length={len(text)}",))

    try:
        logger.debug(f"开始计算token数量，文本长度: {len(text)}")
        encoding = tiktoken.get_encoding("cl100k_base")  # 适用于 OpenAI GPT 系列
        tokens = encoding.encode(text)
        token_count = len(tokens)

        end_time = datetime.now()
        log_execution_time("count_tokens", start_time, end_time)

        logger.debug(f"token计算完成，数量: {token_count}")
        return token_count

    except Exception as e:
        end_time = datetime.now()
        log_execution_time("count_tokens", start_time, end_time)
        logger.error(f"token计算异常: {str(e)}")
        raise


def truncate_text_by_tokens(text: str, max_tokens: int, encoding_name: str = "cl100k_base") -> str:
    """
    根据最大 token 数量截断文本。

    Args:
        text (str): 需要截断的原始文本。
        max_tokens (int): 最大 token 数量。
        encoding_name (str): 使用的编码器名称，默认为 "cl100k_base"。

    Returns:
        str: 截断后的文本。
    """
    start_time = datetime.now()
    log_function_call("truncate_text_by_tokens", args=(f"text_length={len(text)}", max_tokens, encoding_name))

    try:
        logger.debug(f"开始截断文本，原始长度: {len(text)}, 最大token数: {max_tokens}")

        # 获取编码器
        encoding = tiktoken.get_encoding(encoding_name)

        # 将文本编码为 tokens
        tokens = encoding.encode(text)
        original_token_count = len(tokens)

        logger.debug(f"原始token数量: {original_token_count}")

        # 如果 tokens 数量超过最大限制，则截断
        if original_token_count > max_tokens:
            logger.info(f"文本token数({original_token_count})超过限制({max_tokens})，开始截断")
            truncated_tokens = tokens[:max_tokens]
            truncated_text = encoding.decode(truncated_tokens)

            end_time = datetime.now()
            log_execution_time("truncate_text_by_tokens", start_time, end_time)

            logger.info(f"文本截断完成，截断后长度: {len(truncated_text)}, token数: {max_tokens}")
            return truncated_text
        else:
            end_time = datetime.now()
            log_execution_time("truncate_text_by_tokens", start_time, end_time)

            logger.debug("文本token数在限制范围内，无需截断")
            return text

    except Exception as e:
        end_time = datetime.now()
        log_execution_time("truncate_text_by_tokens", start_time, end_time)
        logger.error(f"文本截断异常: {str(e)}")
        raise

if __name__ == '__main__':
    text = "你好，欢迎使用长城云平台！"
    print(count_tokens(text))  
    print(truncate_text_by_tokens(text, 5))  