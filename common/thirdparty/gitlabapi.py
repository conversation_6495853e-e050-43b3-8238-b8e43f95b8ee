# -*- coding: utf-8 -*-
# @Time   : 2025/6/5 11:14
# <AUTHOR> 王志强
# @Email  : <EMAIL>
# @File   : gitlabapi.py
# @Project: ai-service
# @Desc   : 主要实现了xxx功能

import time
import json
import requests
import logging
from datetime import datetime
from common.config.config import *
from common.basic.exception import GitlabApiException
from common.utils.logger_util import get_logger, log_function_call, log_execution_time, log_exception

class GitLabAPI:
    def __init__(self, git_url=GIT_API_URL, token=GIT_PRIVATE_TOKEN):
        self.git_token = token
        self.git_url = git_url
        self.log = logging.getLogger('thirdparty')
        # 使用新的日志系统
        self.logger = get_logger('gitlab_api')
        self.logger.info(f"初始化GitLab API客户端 - URL: {git_url}")

    def get_private_token(self, method):
        return {"private_token": self.git_token}

    def gitlab_request(self, url, method, body='', raise_exception=False, is_json=True):
        if not url.startswith('http'):
            url = f'{self.git_url}{url}'
        header = {"PRIVATE-TOKEN": self.git_token}
        self.log.info('%s %s, body:%s' % (url, method, str(body)))
        _start_time = time.time()
        if method.upper() == 'GET':
            resp = requests.get(url, headers=header)
        elif method.upper() == 'POST':
            resp = requests.post(url, data=body, headers=header)
        elif method.upper() == 'PUT':
            resp = requests.put(url, data=body, headers=header)
        elif method.upper() == 'DELETE':
            resp = requests.delete(url, data=body, headers=header)
        take_time = (time.time() - _start_time)
        if method.upper() == 'GET':
            self.log.info('%s %s [%d] takes:%.2fs' % (url, method, resp.status_code, take_time))
        else:
            self.log.info('%s %s [%d] takes:%.2fs, ret:%s' % (url, method, resp.status_code, take_time, resp.text))
        if resp.status_code in [200, 201, 204]:
            if is_json:
                return resp.status_code, json.loads(resp.text)
            else:
                return resp.status_code, resp.text
        else:
            if raise_exception:
                raise GitlabApiException(f'error_code:{resp.status_code}, error: '+ (resp.text or resp.reason))
        return resp.status_code, resp.text

    def get_mr_changes(self, project_id, iid):
        """
        调用 GitLab API 获取指定合并请求 (MR) 的变更文件信息

        :param project_id: GitLab 项目的 ID
        :param iid: Merge Request (MR) 的内部 ID
        :return: 包含 MR 变更信息的 JSON 数据
        """
        start_time = datetime.now()
        log_function_call("get_mr_changes", args=(project_id, iid))
        self.logger.info(f"开始获取MR变更信息 - 项目ID: {project_id}, MR ID: {iid}")

        url = f"/projects/{project_id}/merge_requests/{iid}/changes"

        try:
            self.logger.debug(f"调用GitLab API: {url}")
            _, data = self.gitlab_request(url, 'GET', raise_exception=True, is_json=True)

            changes_count = len(data.get("changes", []))
            end_time = datetime.now()
            log_execution_time("get_mr_changes", start_time, end_time)

            self.logger.info(f"成功获取MR变更信息 - 项目ID: {project_id}, MR ID: {iid}, 变更文件数: {changes_count}")
            return data

        except Exception as e:
            end_time = datetime.now()
            log_execution_time("get_mr_changes", start_time, end_time)

            self.logger.error(f"获取MR变更信息失败 - 项目ID: {project_id}, MR ID: {iid}")
            log_exception(e, f"GitLab API调用异常 - 项目ID: {project_id}, MR ID: {iid}")

            return {"changes": []}  # 避免返回 None 造成异常

