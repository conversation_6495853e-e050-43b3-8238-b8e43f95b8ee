# -*- coding: utf-8 -*-
# @Time   : 2025/6/5 11:16
# <AUTHOR> 王志强
# @Email  : <EMAIL>
# @File   : exceptiont.py
# @Project: ai-service
# @Desc   : 主要实现了xxx功能

from enum import IntEnum
import traceback
from datetime import datetime

class _BaseException(Exception):
    """base异常"""

    def __init__(self, err):
        Exception.__init__(self)
        self.err = err
        self.timestamp = datetime.now()
        self._log_exception()

    def __str__(self, ):
        return self.err

    def _log_exception(self):
        """记录异常日志"""
        try:
            # 延迟导入避免循环依赖
            from common.utils.logger_util import get_logger
            logger = get_logger("exception")
            logger.error(f"异常创建: {self.__class__.__name__} - {self.err}")
            logger.debug(f"异常堆栈:\n{traceback.format_exc()}")
        except Exception:
            # 如果日志记录失败，不影响异常的正常抛出
            pass


class DBExecException(_BaseException):
    """数据库执行异常"""

    def __init__(self, err):
        super(DBExecException, self).__init__(err)


class ExceptionLevelEnum(IntEnum):
    error = 1
    warning = 2
    info = 3


# 0-999 系统级别
# 0-99 未知异常

class AppException(_BaseException):
    title = '系统内部异常'
    status = 1
    status_code = 200
    level = ExceptionLevelEnum.error

    def __init__(self, message='ERROR'):
        self.message = message
        self.timestamp = datetime.now()
        # 调用父类的日志记录方法
        super().__init__(message)

    def __str__(self):
        return '[%s]%s' % (self.title, self.message)

    def _log_exception(self):
        """重写日志记录方法，记录更详细的应用异常信息"""
        try:
            from common.utils.logger_util import get_logger
            logger = get_logger("exception")
            logger.error(f"应用异常: {self.__class__.__name__} - 状态码: {self.status}, 标题: {self.title}, 消息: {self.message}")
            logger.debug(f"异常级别: {self.level.name}, 时间: {self.timestamp}")
        except Exception:
            pass

class GitlabApiException(AppException):
    status, title = 5001, 'gitlab api 请求异常'

    def _log_exception(self):
        """记录GitLab API异常"""
        try:
            from common.utils.logger_util import get_logger
            logger = get_logger("gitlab_api")
            logger.error(f"GitLab API异常: {self.message}")
            logger.debug(f"异常详情: 状态码={self.status}, 时间={self.timestamp}")
        except Exception:
            pass

class SystemException(AppException):
    status, title = 8888, '内部系统错误'

    def _log_exception(self):
        """记录系统异常"""
        try:
            from common.utils.logger_util import get_logger
            logger = get_logger("system")
            logger.critical(f"系统异常: {self.message}")
            logger.debug(f"异常详情: 状态码={self.status}, 时间={self.timestamp}")
        except Exception:
            pass

class MRFileChangeException(AppException):
    status, title = 4001, 'MR 变更文件不符合要求'

    def _log_exception(self):
        """记录MR文件变更异常"""
        try:
            from common.utils.logger_util import get_logger
            logger = get_logger("mr_file_change")
            logger.warning(f"MR文件变更异常: {self.message}")
            logger.debug(f"异常详情: 状态码={self.status}, 时间={self.timestamp}")
        except Exception:
            pass

class MRFetchException(AppException):
    status, title = 4002, '获取 MR 信息失败'

    def _log_exception(self):
        """记录MR获取异常"""
        try:
            from common.utils.logger_util import get_logger
            logger = get_logger("mr_fetch")
            logger.error(f"MR获取异常: {self.message}")
            logger.debug(f"异常详情: 状态码={self.status}, 时间={self.timestamp}")
        except Exception:
            pass
