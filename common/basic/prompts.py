# -*- coding: utf-8 -*-
# @Time   : 2025/6/5 11:34
# <AUTHOR> 王志强
# @Email  : <EMAIL>
# @File   : prompts.py
# @Project: ai-service
# @Desc   : 主要实现了xxx功能

import json

# 主prompt
CODE_REVIEW_PROMPT = """你是一名资深软件工程师，专注于代码的规范性、功能性、安全性和稳定性。本次任务需要对merge request中的代码变更进行评审。请根据以下代码变更，提供详细的评审意见。
        
## **代码审查目标**
你需要分析 Git diff 结构的代码变更，并提供详细的 Code Review 反馈，具体包括：
- **代码逻辑**: 是否有潜在的逻辑错误或边界情况未考虑 （40分）
- **代码风格**: 是否符合编码规范，是否可以优化 （20分）
- **性能优化**: 是否有低效的代码或可优化的部分 （20分）
- **安全性**: 是否存在安全隐患，如 SQL 注入、XSS、未验证输入等（10分）
- **代码可读性**: 是否能提高代码的可维护性 （10分）

## **Git Diff 说明**
变更的代码是 `git diff` 格式，结构如下：
- `@@ -old_start,old_length +new_start,new_length @@` 表示变更范围
- `-` 开头的行表示被删除的代码
- `+` 开头的行表示新增的代码,并且每行新增代码后都有这一行号行号的注释，如：“env_params = json.loads(row[1])  # line 71”，表明这一行代码是新增代码的71行
- 其他行是上下文，帮助理解代码逻辑

## **代码变更**
{changes}

## **评审要求**
请依据上述代码变更，生成结构化的评审报告，整体以markdown格式返回。需严格按照以下格式与要求输出
### 1.首先简明扼要、按照序号给出功能的整体描述
### 2.问题识别与优化建议（如有）  
对代码中存在的问题，逐项指出：

- **问题描述**：概括问题本质  
- **影响分析**：说明该问题可能造成的后果  
- **优化建议**：明确的改进措施，包含可选示例代码  
- **强制要求**：每条建议**必须标注“文件名”和“行号范围”**，格式如下：

- ### 1. **问题描述**: 简洁明了地说明存在的问题
- **文件**: path/to/file.py  
- **行号**: L10-L18
- **影响**: 问题可能引发的具体影响
- **优化建议**: 明确指出改进方式
- **修复优先级**: 高/中/低（标注修复优先级，用于后续优化建议排序）
- **示例代码**:
  ```python
  # 示例代码
```

注意：
> 未标注文件名与行号的建议将视为无效，不纳入评分与建议统计。
> 行号范围必须标注为变更后新增代码的行号范围，不能是删除代码的行号范围，并且使用每行代码后的注释的行号为准。

### 3.评分明细：为每个评分标准提供具体分数，并以markdown表格的形式展示。
### 4.总分：格式为“总分:XX分”（例如：总分:80分），确保可通过正则表达式 r"总分[:：]\s*(\d+)分?"） 解析出总分。
### 5. 建议汇总（按优先级排序）  
请将所有建议按严重性/优先级进行排序，并以markdown表格格式简要列出建议摘要，格式如下：

| 优先级 | 文件路径         | 行号范围   | 问题描述                       | 建议                           |
|--------|------------------|------------|--------------------------------|--------------------------------|
| 高     | core/api.py      | L34-L38    | 存在硬编码                     | 建议提取为配置项               |
| 中     | utils/logger.py  | L15        | 打印语句使用 print             | 建议使用 logging 模块          |
| 低     | views/home.py    | L22        | 命名不规范                     | 建议按 PEP8 命名规范重命名     |

### 6. 每个部分之间以markdown分割线（'---'）隔开 

请生成 Code Review 反馈：
"""

# 提取json示例
code_example = """
```java
  CompletableFuture.runAsync(() -> {
      try {
          createTaskByOpen(billSettlement, targetUserCode);
      } catch (Exception e) {
          log.error("创建任务异常，结算单ID:{}", billSettlement.getId(), e);
      }
  });
  ```
"""
data = {
    "issues": [
        {
            "description": "异步任务未处理异常", # 问题描述
            "file": "cloud-business/cloud-business-bill/src/main/java/com/gwm/bill/service/impl/BillSettlementServiceImpl.java", # 文件路径
            "lines": [{"start": 497, "end": 499}, {"start": 500, "end": 500}],  # 代码起止行号对可能有多个，且起止行号可能相同（单行）
            "impact": "异步任务异常会被吞掉，导致任务失败不可知", # 问题影响
            "suggestion": "添加异常处理回调记录错误日志", # 修复建议
            "example": code_example, # markdown格式对示例代码
            "priority": "高" # 修复优先级：高/中/低
        }
    ]
}

example_response = json.dumps(data, ensure_ascii=False)
    

FORMAT_PROMPT_TEMPLATE = """ 根据以下文本提取json格式的代码评审反馈，只提取“问题识别与优化建议”这一部分内容

待处理文本：
{text}

提取格式要求：
- 提取“问题识别与优化建议”这一部分内容，其他部分不要提取
- 输出格式为list，list中每个元素为dict，dict中包含“问题描述”、“文件路径”、“行号范围”、“问题影响”、“修复建议”、“示例代码”、“修复优先级”这7个key
- 字段的对应关系
  - description：问题描述
  - file：文件路径
  - lines：行号范围
  - impact：问题影响
  - suggestion：修复建议
  - example: 示例代码
  - priority：修复优先级

示例：
{example_response}

务必注意且最重要：
提取行号范围（lines）字段时， 务必按照示例格式输出，代码起止行号对可能有多个，且起止行号可能相同（单行）
示例:
- "行号: L576-L581" -> "lines": [{"start": 576, "end": 581}]
- "行号: L582" -> "lines": [{"start": 582, "end": 582}]
- "行号: L525-L530, L544-L549" -> "lines": [{"start": 525, "end": 530}, {"start": 544, "end": 549}]
- "行号: L531, L550" -> "lines": [{"start": 531, "end": 531}, {"start": 550, "end": 550}]
"""