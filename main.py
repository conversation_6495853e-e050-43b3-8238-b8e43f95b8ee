from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi import Query
import threading
import asyncio
import time
from service.code_review import review_mr_changes
from fastapi import (
    FastAPI,
    WebSocket,
    WebSocketDisconnect,
)

from common.basic.exception import AppException, SystemException
from common.utils.result_util import get_ret_result

app = FastAPI()

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许的源，可以替换为特定域名，例如 ["http://localhost:8080"]
    allow_credentials=True,
    allow_methods=["*"],  # 允许的请求方法（GET, POST, PUT, DELETE等）
    allow_headers=["*"],  # 允许的请求头
)
# 测试连通性
@app.get("/api/test")
async def test():
    return get_ret_result(data = {
            "result": "ok"
        })

@app.get("/api/mr/code_review")
async def mr_code_review(
        project_id: str, 
        mr_iid: str,
        json_mode: bool = Query(False, description="是否以 JSON 模式返回结果")
    ):
    try:
        data, formatted_res = review_mr_changes(project_id, mr_iid, json_mode=json_mode)
        return get_ret_result(data = {
            "result": data,
            "formatted_result": formatted_res
        })
    except AppException as e:
        return get_ret_result(status=e.status, message=e.message)
    except Exception as e:
        import traceback
        print(traceback.format_exc())
        return get_ret_result(status=SystemException.status, message=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8888)