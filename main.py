# -*- coding: utf-8 -*-
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
import threading
import asyncio
import time
from datetime import datetime
from service.code_review import review_mr_changes, review_mr_changes_async, get_concurrent_status, update_concurrent_limit
from fastapi import (
    FastAPI,
    WebSocket,
    WebSocketDisconnect,
    Request,
)

from common.basic.exception import AppException, SystemException
from common.utils.result_util import get_ret_result
from common.utils.logger_util import init_logger, get_logger, log_exception, log_function_call, log_execution_time

# 初始化日志
logger = init_logger("ai-service", "INFO")
logger.info("正在初始化AI服务应用...")

app = FastAPI(title="AI代码审查服务", version="1.0.0")

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许的源，可以替换为特定域名，例如 ["http://localhost:8080"]
    allow_credentials=True,
    allow_methods=["*"],  # 允许的请求方法（GET, POST, PUT, DELETE等）
    allow_headers=["*"],  # 允许的请求头
)

# 请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = datetime.now()
    client_ip = request.client.host if request.client else "unknown"

    logger.info(f"收到请求: {request.method} {request.url} - 客户端IP: {client_ip}")

    try:
        response = await call_next(request)
        end_time = datetime.now()
        process_time = (end_time - start_time).total_seconds()

        logger.info(f"请求完成: {request.method} {request.url} - 状态码: {response.status_code} - 耗时: {process_time:.3f}秒")
        return response
    except Exception as e:
        end_time = datetime.now()
        process_time = (end_time - start_time).total_seconds()

        logger.error(f"请求异常: {request.method} {request.url} - 耗时: {process_time:.3f}秒")
        log_exception(e, f"处理请求 {request.method} {request.url} 时发生异常")
        raise

logger.info("AI服务应用初始化完成")
# 测试连通性
@app.get("/api/test")
async def test():
    """健康检查接口"""
    logger.info("执行健康检查")

    try:
        result = get_ret_result(data={
            "result": "ok",
            "timestamp": datetime.now().isoformat(),
            "service": "ai-service"
        })
        logger.info("健康检查成功")
        return result
    except Exception as e:
        logger.error("健康检查失败")
        log_exception(e, "健康检查接口异常")
        return get_ret_result(status=SystemException.status, message="健康检查失败")

@app.get("/api/mr/code_review")
async def mr_code_review(project_id: str, mr_iid: str):
    """MR代码审查接口 - 异步版本"""
    start_time = datetime.now()

    # 记录请求参数
    log_function_call("mr_code_review", args=(project_id, mr_iid))
    logger.info(f"开始执行异步MR代码审查 - 项目ID: {project_id}, MR ID: {mr_iid}")

    try:
        # 调用异步代码审查服务
        data = await review_mr_changes_async(project_id, mr_iid)

        end_time = datetime.now()
        log_execution_time("mr_code_review", start_time, end_time)

        logger.info(f"异步MR代码审查完成 - 项目ID: {project_id}, MR ID: {mr_iid}")

        return get_ret_result(data={
            "result": str(data),
            "project_id": project_id,
            "mr_iid": mr_iid,
            "timestamp": end_time.isoformat()
        })

    except AppException as e:
        end_time = datetime.now()
        log_execution_time("mr_code_review", start_time, end_time)

        logger.warning(f"异步MR代码审查业务异常 - 项目ID: {project_id}, MR ID: {mr_iid}, 错误: {e.message}")
        return get_ret_result(status=e.status, message=e.message)

    except Exception as e:
        end_time = datetime.now()
        log_execution_time("mr_code_review", start_time, end_time)

        logger.error(f"异步MR代码审查系统异常 - 项目ID: {project_id}, MR ID: {mr_iid}")
        log_exception(e, f"异步MR代码审查接口异常 - 项目ID: {project_id}, MR ID: {mr_iid}")

        return get_ret_result(status=SystemException.status, message=str(e))


@app.get("/api/mr/code_review_sync")
async def mr_code_review_sync(project_id: str, mr_iid: str):
    """MR代码审查接口 - 同步版本（保留作为备用）"""
    start_time = datetime.now()

    # 记录请求参数
    log_function_call("mr_code_review_sync", args=(project_id, mr_iid))
    logger.info(f"开始执行同步MR代码审查 - 项目ID: {project_id}, MR ID: {mr_iid}")

    try:
        # 调用同步代码审查服务
        data = review_mr_changes(project_id, mr_iid)

        end_time = datetime.now()
        log_execution_time("mr_code_review_sync", start_time, end_time)

        logger.info(f"同步MR代码审查完成 - 项目ID: {project_id}, MR ID: {mr_iid}")

        return get_ret_result(data={
            "result": str(data),
            "project_id": project_id,
            "mr_iid": mr_iid,
            "timestamp": end_time.isoformat()
        })

    except AppException as e:
        end_time = datetime.now()
        log_execution_time("mr_code_review_sync", start_time, end_time)

        logger.warning(f"同步MR代码审查业务异常 - 项目ID: {project_id}, MR ID: {mr_iid}, 错误: {e.message}")
        return get_ret_result(status=e.status, message=e.message)

    except Exception as e:
        end_time = datetime.now()
        log_execution_time("mr_code_review_sync", start_time, end_time)

        logger.error(f"同步MR代码审查系统异常 - 项目ID: {project_id}, MR ID: {mr_iid}")
        log_exception(e, f"同步MR代码审查接口异常 - 项目ID: {project_id}, MR ID: {mr_iid}")

        return get_ret_result(status=SystemException.status, message=str(e))


@app.get("/api/admin/concurrent_status")
async def get_concurrent_status_api():
    """获取当前并发状态信息"""
    try:
        status = get_concurrent_status()
        logger.info("获取并发状态信息成功")
        return get_ret_result(data=status)
    except Exception as e:
        logger.error("获取并发状态信息失败")
        log_exception(e, "获取并发状态信息异常")
        return get_ret_result(status=SystemException.status, message=str(e))


@app.post("/api/admin/update_concurrent_limit")
async def update_concurrent_limit_api(new_limit: int):
    """更新并发限制数量"""
    try:
        result = update_concurrent_limit(new_limit)
        logger.info(f"更新并发限制成功: {result}")
        return get_ret_result(data=result)
    except ValueError as e:
        logger.warning(f"更新并发限制参数错误: {e}")
        return get_ret_result(status=400, message=str(e))
    except Exception as e:
        logger.error("更新并发限制失败")
        log_exception(e, "更新并发限制异常")
        return get_ret_result(status=SystemException.status, message=str(e))


if __name__ == "__main__":
    import uvicorn

    logger.info("正在启动AI服务...")
    logger.info("服务配置: host=0.0.0.0, port=8888")

    try:
        uvicorn.run(app, host="0.0.0.0", port=8888)
    except Exception as e:
        logger.critical("服务启动失败")
        log_exception(e, "服务启动异常")
        raise
    finally:
        logger.info("AI服务已停止")