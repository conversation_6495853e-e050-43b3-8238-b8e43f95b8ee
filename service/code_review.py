# -*- coding: utf-8 -*-
# @Time   : 2025/03/18 14:30
# <AUTHOR> 和森
# @Email  : <EMAIL>
# @File   : ai_cr.py
# @Project: app-service
# @Desc   : 主要实现了使用大模型进行CR建议的功能

import sys
sys.path.append('/Users/<USER>/ai-service')  # 添加这一行在导入common模块之前

from common.config.config import (LLM_MODEL, LLM_API_KEY, LLM_API_URL, REVIEW_MAX_TOKENS, REVIEW_MAX_CODE_PLUS, LLM_FORMAT_MODLE)
from common.basic.prompts import CODE_REVIEW_PROMPT, FORMAT_PROMPT_TEMPLATE, example_response
from common.thirdparty.gitlabapi import Git<PERSON>abAPI
from common.utils.token_util import count_tokens, truncate_text_by_tokens
from common.utils.git_diff_format import annotate_added_line_numbers
from common.basic.exception import GitlabApiException, MRFileChangeException, MRFetchException
import sys
import requests
import json
import fnmatch
from llama_index.llms.openai import OpenAI
from llama_index.core.llms import ChatMessage
from llama_index.core.prompts import PromptTemplate


# 定义指定文件类型
SPECIFIC_FILE_TYPES = [
    "*.go",  # 只处理 Go 语言文件
    "*.java",  # 只处理 Java 语言文件
    "*.py",  # 只处理 Python 语言文件
    "*.js",  # 只处理 JavaScript 语言文件
    "*.ts",  # 只处理 TypeScript 语言文件
]

def filter_files(file_list):
    """过滤掉匹配 IGNORE_PATTERNS 规则的文件"""
    filtered_files = []
    for file in file_list:
        if not any(fnmatch.fnmatch(file, pattern) for pattern in SPECIFIC_FILE_TYPES):
            continue
        filtered_files.append(file)
    return filtered_files

def filter_mr_changes(project_id, iid):
    """
    获取 MR 变更信息，并只处理 SPECIFIC_FILE_TYPES 中的文件类型
    
    :param project_id: GitLab 项目的 ID
    :param iid: Merge Request (MR) 的内部 ID
    :return: 返回字典包含:
             - data: 过滤后的变更文件列表(可能为空)
             - status: 执行状态(True/False)
             - reason: 当status为False时的原因说明
    """
    try:
        res = GitLabAPI().get_mr_changes(project_id, iid)
        changes = res.get("changes", [])

        print(f"===raw changes==:{changes}")
        
        if not changes:
            raise MRFileChangeException("MR 中没有文件变更")
           
        filtered_changes = []
        total_diff_plus = 0
        for change in changes:
            new_path = change.get("new_path", "")
            diff = change.get("diff", "")
            print("===diff===")
            print(diff)
            diff = annotate_added_line_numbers(diff)
            print("===annotated diff===")
            print(diff)
            
            # 检查是否为指定文件类型
            if not any(fnmatch.fnmatch(new_path, pattern) for pattern in SPECIFIC_FILE_TYPES):
                continue

            # 计算新增行数
            diff_plus = diff.count("\n+")
            total_diff_plus += diff_plus 
            filtered_changes.append({"new_path": new_path, "diff": diff})
            
        print(f"total_diff_plus:{total_diff_plus}")
        if not filtered_changes:
            raise MRFileChangeException("MR 中没有需要处理的代码变更(所有变更文件都不在指定类型范围内)")
        elif total_diff_plus > REVIEW_MAX_CODE_PLUS:
            # 新增变更行数超过限制，不进行AI代码评审
            raise MRFileChangeException(f"MR 中需要处理的代码新增变更行数超过{REVIEW_MAX_CODE_PLUS}，不进行AI代码评审.")

        return filtered_changes
        
    except MRFileChangeException:
        raise
    except Exception as e:
        raise MRFetchException(f"获取 MR 变更时发生错误: {str(e)}")


def review_mr_changes(project_id, iid, json_mode=False):
    """
    评审 MR 代码变更，调用大模型生成评审意见

    :param project_id: GitLab 项目的 ID
    :param iid: Merge Request 的 ID
    :return: 大模型生成的代码评审意见，如果没有需要评审的内容则m返回原因
    """

    print(f"开始分析项目:{project_id} MR:{iid} 变更内容...")
    result = filter_mr_changes(project_id, iid)
 
    # 定义 PromptTemplate，强调 git diff 解析
    prompt_template = PromptTemplate(CODE_REVIEW_PROMPT)

    # 格式化 changes 内容
    changes_str = "\n\n".join(
        [f"### 文件: {change['new_path']}\n```diff\n{change['diff']}\n```" for change in result]
    )

    # 计算tokens数量，如果超过REVIEW_MAX_TOKENS，截断changes_text
    total_tokens = count_tokens(changes_str)
    print(f"total_tokens:{total_tokens}")

    if total_tokens > REVIEW_MAX_TOKENS:
        print(f"AI代码评审：变更内容超过最大token限制({REVIEW_MAX_TOKENS})，将进行截断。")
        changes_str = truncate_text_by_tokens(changes_str, REVIEW_MAX_TOKENS)


    # 使用 PromptTemplate 生成最终的 prompt
    review_prompt = prompt_template.format(changes=changes_str)

    print(f"review_prompt:{review_prompt}")

    print("开始调用大模型...")
    # 调用 LlamaIndex 中的大模型
    llm = OpenAI(
        api_key=LLM_API_KEY,
        api_base=LLM_API_URL,
        default_headers={"model": LLM_MODEL}
    )

    messages = [ChatMessage(role="user", content=review_prompt)]
    response = llm.chat(messages=messages)
    final_response = str(response)
    
    print("AI代码评审成功.")
    print(f"raw response:{response}")

    formatted_response = {}
    if json_mode:
        try:
            print("开始格式化LLM输出为JSON...")
            formatted_response = format_llm_response(response.message.content)
            print(f"formatted response:{formatted_response}")
        except Exception as e:
            print(f"格式化LLM输出为JSON失败:{e}")
            
    return final_response,formatted_response

def format_llm_response(input:str) -> dict:
    """
    格式化LLM输出为JSON格式

    :param input: 原始LLM输出字符串
    :return: 包含code_review字段的字典
    """
    prompt_template = PromptTemplate(FORMAT_PROMPT_TEMPLATE)
    json_prompt = prompt_template.format(text=input, example_response=example_response)
    print(f"json_prompt:{json_prompt}")

    llm = OpenAI(
        api_key=LLM_API_KEY,
        api_base=LLM_API_URL,
        default_headers={"model": LLM_FORMAT_MODLE},
        additional_kwargs={ 
        "response_format": {"type": "json_object"}
    }
    )

    messages = [ChatMessage(role="user", content=json_prompt)]
    response = llm.chat(messages=messages)
    print(response)
    try:
        data = json.loads(response.message.content)
        print(data)
    except json.JSONDecodeError:
        print("JSON解析错误")

    return data

if __name__ == "__main__":
        changed_files = [
        "src/main.py",
        "docs/readme.md",
        "config/settings.yaml",
        "data/schema.proto",
        "vendor/lib/dependency.txt",
        "src/module.py",
        "src/module.go",
        "src/module.java",
        "src/module.js",
        "src/module.ts",
        "src/module.pyc",
    ]
        # filtered_files = filter_files(changed_files)
        # print("过滤后的文件列表:") 
        # print(filtered_files)
        # res = review_mr_changes(4142, 306)
        # res = review_mr_changes(2609, 995)
        # res = review_mr_changes(6900,2693)
        # print(f"res:{res}")
        # filter_mr_changes(4142, 306) 
        response = """
        ### 1. 功能整体描述
1. 在BillSettlement实体类中新增taskId字段，用于关联待办任务ID
2. 新增TaskModel类作为任务数据模型，包含任务创建/执行人、标题、参数等字段
3. 在BillSettlementServiceImpl中实现异步任务创建与完成功能：
   - 新增配置参数注入（appKey、secret、API地址等）
   - 新增createTask和completeTask异步方法
   - 实现与4A平台对接的任务创建/完成逻辑
   - 使用OpenClient进行HTTP通信
   - 使用CompletableFuture实现异步处理

---

### 2. 问题识别与优化建议

#### 1. **问题描述**: 异步任务未处理异常
- **文件**: cloud-business/cloud-business-bill/src/main/java/com/gwm/bill/service/impl/BillSettlementServiceImpl.java  
- **行号**: L497-L499, L512-L514
- **影响**: 异步任务异常未捕获可能导致任务静默失败，影响系统可靠性
- **优化建议**: 添加try-catch并记录异常日志
- **修复优先级**: 高
- **示例代码**:
  ```java
  CompletableFuture.runAsync(() -> {
      try {
          createTaskByOpen(billSettlement, targetUserCode);
      } catch (Exception e) {
          log.error("创建任务异常，结算单ID:{}", billSettlement.getId(), e);
      }
  });
  ```

#### 2. **问题描述**: OpenClient实例未复用
- **文件**: cloud-business/cloud-business-bill/src/main/java/com/gwm/bill/service/impl/BillSettlementServiceImpl.java  
- **行号**: L522, L541
- **影响**: 频繁创建HTTP客户端可能导致资源泄漏和性能问题
- **优化建议**: 改为单例模式或使用连接池
- **修复优先级**: 中
- **示例代码**:
  ```java
  @Autowired
  private OpenClient openClient; // 通过Spring管理生命周期
  ```

#### 3. **问题描述**: 日志记录包含敏感信息
- **文件**: cloud-business/cloud-business-bill/src/main/java/com/gwm/bill/service/impl/BillSettlementServiceImpl.java  
- **行号**: L525-L530, L544-L549
- **影响**: 可能泄露API密钥、业务数据等敏感信息
- **优化建议**: 对敏感字段进行脱敏处理
- **修复优先级**: 高
- **示例代码**:
  ```java
  String safeParam = param.toJSONString().replaceAll("(\"appKey\":\")[^\"]+", "$1***");
  String msg = String.format("请求参数：%s, 响应参数：%s", safeParam, json);
  ```

#### 4. **问题描述**: 未验证HTTP响应格式
- **文件**: cloud-business/cloud-business-bill/src/main/java/com/gwm/bill/service/impl/BillSettlementServiceImpl.java  
- **行号**: L531, L550
- **影响**: 可能因格式错误导致NullPointerException
- **优化建议**: 增加空值校验和格式验证
- **修复优先级**: 中
- **示例代码**:
  ```java
  if (jsonObject != null && jsonObject.containsKey("code")) {
      // 处理逻辑
  }
  ```

#### 5. **问题描述**: TaskModel中priority字段类型不当
- **文件**: cloud-business/cloud-business-bill/src/main/java/com/gwm/bill/model/TaskModel.java  
- **行号**: L41
- **影响**: 使用String类型可能导致无效值传入
- **优化建议**: 改用枚举类型或Integer类型
- **修复优先级**: 中
- **示例代码**:
  ```java
  public enum PriorityLevel {
      LOW(0), NORMAL(1), MEDIUM(2), HIGH(3);
      private final int value;
      // 枚举实现
  }
  ```

#### 6. **问题描述**: URL拼接存在注入风险
- **文件**: cloud-business/cloud-business-bill/src/main/java/com/gwm/bill/service/impl/BillSettlementServiceImpl.java  
- **行号**: L576-L581
- **影响**: 可能导致URL参数注入攻击
- **优化建议**: 使用URI构建器进行参数编码
- **修复优先级**: 高
- **示例代码**:
  ```java
  String actionParameter = UriComponentsBuilder.fromHttpUrl(actionUrl)
      .queryParam("id", billSettlement.getId())
      .queryParam("start", billSettlement.getSettlementPeriodStart())
      .build().toUriString();
  ```

---

### 3. 评分明细

| 评分标准     | 得分 | 说明 |
|--------------|------|------|
| 代码逻辑     | 32/40 | 存在未处理异常、参数校验缺失等问题 |
| 代码风格     | 16/20 | 注释规范但部分命名可优化 |
| 性能优化     | 14/20 | 存在客户端重复创建问题 |
| 安全性       | 6/10  | 存在日志敏感信息泄露风险 |
| 代码可读性   | 8/10  | 部分重复代码可提取公共方法 |

---

### 4. 总分
总分:76分

---

### 5. 建议汇总（按优先级排序）

| 优先级 | 文件路径         | 行号范围   | 问题描述                       | 建议                           |
|--------|------------------|------------|--------------------------------|--------------------------------|
| 高     | BillSettlementServiceImpl.java | L497-L499  | 异步任务未处理异常             | 添加try-catch捕获并记录异常    |
| 高     | BillSettlementServiceImpl.java | L576-L581  | URL拼接存在注入风险            | 使用URI构建器进行参数编码      |
| 高     | BillSettlementServiceImpl.java | L525-L530  | 日志记录包含敏感信息           | 对敏感字段进行脱敏处理         |
| 中     | TaskModel.java   | L41        | priority字段类型不当           | 改用枚举类型或Integer类型      |
| 中     | BillSettlementServiceImpl.java | L531       | 未验证HTTP响应格式             | 增加空值校验和格式验证         |
| 中     | BillSettlementServiceImpl.java | L522       | OpenClient实例未复用           | 改为单例模式或使用连接池       |

---

### 6. 其他建议
1. 建议为TaskModel的param字段添加泛型参数 `<T>` 提高类型安全性
2. 建议将OpenAPI的URL配置合并到配置类中统一管理
3. 建议为CompletableFuture配置自定义线程池提升并发性能
4. 建议在createTaskByOpen方法中添加重试机制
5. 建议将日志记录的msg构造提取为公共方法减少重复代码
        """

        format_llm_response(response)