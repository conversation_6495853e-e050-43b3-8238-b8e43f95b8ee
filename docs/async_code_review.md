# 异步代码审查功能使用指南

## 概述

本文档介绍了AI服务中异步代码审查功能的使用方法和并发控制机制。

## 功能特性

### 1. 异步LLM调用
- 将耗时的大模型调用改为异步操作
- 支持多个请求并发处理
- 避免阻塞其他请求

### 2. 并发控制
- 可配置的最大并发数量
- 使用信号量控制并发访问
- 线程池管理LLM调用

### 3. 动态配置
- 运行时调整并发限制
- 实时监控并发状态
- 管理接口支持

## API接口

### 主要接口

#### 1. 异步代码审查（推荐）
```
GET /api/mr/code_review?project_id={project_id}&mr_iid={mr_iid}
```

**特点：**
- 异步处理，支持并发
- 自动并发控制
- 更好的性能表现

#### 2. 同步代码审查（备用）
```
GET /api/mr/code_review_sync?project_id={project_id}&mr_iid={mr_iid}
```

**特点：**
- 传统同步处理
- 作为备用方案
- 兼容旧版本

### 管理接口

#### 1. 获取并发状态
```
GET /api/admin/concurrent_status
```

**响应示例：**
```json
{
  "status": 200,
  "message": "success",
  "data": {
    "max_concurrent_calls": 3,
    "available_permits": 2,
    "active_calls": 1,
    "thread_pool_active": 1,
    "thread_pool_max_workers": 3
  }
}
```

#### 2. 更新并发限制
```
POST /api/admin/update_concurrent_limit
Content-Type: application/json

{
  "new_limit": 5
}
```

**响应示例：**
```json
{
  "status": 200,
  "message": "success",
  "data": {
    "success": true,
    "old_limit": 3,
    "new_limit": 5,
    "message": "并发限制已从 3 更新为 5"
  }
}
```

## 配置说明

### 配置文件位置
`common/config/config.py`

### 相关配置项
```python
# 并发控制配置
MAX_CONCURRENT_LLM_CALLS = 3  # 默认最大并发LLM调用数
LLM_TIMEOUT_SECONDS = 300     # LLM调用超时时间（秒）
```

### 配置建议
- **小型部署**: 1-2个并发
- **中型部署**: 3-5个并发
- **大型部署**: 5-10个并发

**注意**: 并发数过高可能导致：
- API限流
- 内存占用增加
- 响应时间变长

## 使用示例

### Python客户端示例
```python
import asyncio
import aiohttp

async def review_code_async(project_id: str, mr_iid: str):
    """异步调用代码审查接口"""
    url = f"http://localhost:8888/api/mr/code_review"
    params = {
        "project_id": project_id,
        "mr_iid": mr_iid
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.get(url, params=params) as response:
            return await response.json()

# 并发处理多个MR
async def batch_review():
    tasks = [
        review_code_async("123", "456"),
        review_code_async("123", "457"),
        review_code_async("123", "458"),
    ]
    
    results = await asyncio.gather(*tasks)
    return results

# 运行
results = asyncio.run(batch_review())
```

### curl示例
```bash
# 异步代码审查
curl "http://localhost:8888/api/mr/code_review?project_id=123&mr_iid=456"

# 查看并发状态
curl "http://localhost:8888/api/admin/concurrent_status"

# 更新并发限制
curl -X POST "http://localhost:8888/api/admin/update_concurrent_limit" \
     -H "Content-Type: application/json" \
     -d '{"new_limit": 5}'
```

## 监控和调优

### 性能监控
1. 查看并发状态：`GET /api/admin/concurrent_status`
2. 观察日志中的执行时间
3. 监控系统资源使用情况

### 调优建议
1. **根据负载调整并发数**：
   - 高负载时增加并发数
   - 低负载时减少并发数

2. **监控API限流**：
   - 观察LLM API的响应时间
   - 避免触发API限流

3. **内存管理**：
   - 监控内存使用情况
   - 适当调整并发数

## 故障排除

### 常见问题

#### 1. 请求超时
**原因**: 并发数设置过高或LLM服务响应慢
**解决**: 减少并发数或增加超时时间

#### 2. 内存不足
**原因**: 并发数过高导致内存占用过多
**解决**: 减少并发数

#### 3. API限流
**原因**: 请求频率过高触发LLM API限流
**解决**: 减少并发数或增加请求间隔

### 日志分析
关键日志信息：
- `等待获取LLM调用许可`: 表示请求在等待并发槽位
- `获得LLM调用许可`: 表示开始处理请求
- `大模型调用成功，耗时: X秒`: 显示LLM调用耗时

## 最佳实践

1. **合理设置并发数**: 根据系统资源和API限制设置
2. **监控系统状态**: 定期检查并发状态和性能指标
3. **渐进式调优**: 逐步调整并发数，观察效果
4. **错误处理**: 实现适当的重试和降级机制
5. **日志记录**: 保持详细的日志记录便于问题排查
